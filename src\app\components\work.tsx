'use client';
import React, { useRef } from "react";
import { useTransform, motion, useScroll, MotionValue } from 'framer-motion';

const ORANGE = "#e8561c";

const workImages = [
  '/img/work/Screenshot 2025-06-28 164306.png',
  '/img/work/Screenshot 2025-06-28 164452.png',
  '/img/work/Screenshot 2025-06-28 164758.png'
];

interface ImageCardProps {
  i: number;
  src: string;
  progress: MotionValue<number>;
  range: [number, number];
  targetScale: number;
}

const ImageCard: React.FC<ImageCardProps> = ({
  i,
  src,
  progress,
  range,
  targetScale,
}) => {
  const container = useRef(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ['start end', 'start start'],
  });

  const scale = useTransform(progress, range, [1, targetScale]);

  return (
    <div
      ref={container}
      style={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'sticky',
        top: 0,
      }}
    >
      <motion.div
        style={{
          scale,
          top: `calc(5vh + ${i * 25}px)`,
          position: 'relative',
          width: '1400px',
          height: '750px',
          borderRadius: '25px',
          overflow: 'hidden',
          boxShadow: '0 25px 50px rgba(0,0,0,0.2)',
        }}
      >
        <img 
          src={src} 
          alt={`Work project ${i + 1}`}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        />
      </motion.div>
    </div>
  );
};

const Work = () => {
  const container = useRef(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ['start start', 'end end'],
  });

  // Einfache responsive Funktion basierend auf Viewport-Einheiten
  const getResponsiveFontSize = (base: number) => {
    return `clamp(${base * 0.5}px, ${base * 0.08}vw, ${base}px)`;
  };

  const getResponsiveSpacing = (base: number) => {
    return `clamp(${base * 0.5}px, ${base * 0.1}vw, ${base}px)`;
  };

  return (
    <section
      ref={container}
      style={{
        background: "#f0f0f0",
        width: "100%",
        minHeight: "100vh",
        fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        position: 'relative',
      }}
    >
      {/* Top label - WORK at very left of section */}
      <div style={{
        position: 'absolute',
        top: 120,
        left: 40,
        color: '#000',
        fontSize: 28,
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (WORK)
      </div>
      {/* Right label - 02 etwa 2 Zeilen über der ersten Trennlinie */}
      <div style={{
        position: 'absolute',
        top: 'clamp(420px, 50vh, 580px)',
        right: 'clamp(20px, 3vw, 40px)',
        color: ORANGE,
        fontSize: getResponsiveFontSize(42),
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (02)
      </div>

      <div style={{
        maxWidth: 1200,
        margin: '0 auto',
        padding: 'clamp(20px, 3vw, 40px)',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        minHeight: '100vh',
        marginTop: 80,
        position: 'relative',
      }}>
        {/* Main content with responsive padding */}
        <div style={{ padding: '0' }}>
          {/* Intro - much larger */}
          <div style={{
            color: '#000',
            fontSize: getResponsiveFontSize(64),
            fontWeight: 700,
            margin: `${getResponsiveSpacing(180)} 0 ${getResponsiveSpacing(80)} 0`,
            maxWidth: 900,
            alignSelf: 'flex-start',
            lineHeight: 1.15,
            marginLeft: 'clamp(-150px, -10vw, 0px)',
          }}>
            Selected Work<br />
            <span style={{
              fontWeight: 400,
              fontSize: getResponsiveFontSize(40),
              display: 'block',
              marginTop: getResponsiveSpacing(32),
              lineHeight: 1.2,
            }}>
              A collection of projects that showcase my approach to solving complex problems through thoughtful design and clean code.
            </span>
          </div>

          {/* Motion Stacking Images Section */}
          <div style={{
            width: '100%',
            background: '#f0f0f0',
          }}>
            {workImages.map((image, i) => {
              const targetScale = 1 - (workImages.length - i) * 0.05;
              return (
                <ImageCard
                  key={`p_${i}`}
                  i={i}
                  src={image}
                  progress={scrollYProgress}
                  range={[i * 0.25, 1]}
                  targetScale={targetScale}
                />
              );
            })}
          </div>

          {/* Bottom spacing */}
          <div style={{
            height: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
            <div style={{
              textAlign: 'center',
              color: '#666',
              fontSize: 18,
              fontWeight: 500,
            }}>
              Scroll to explore more work
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Work;

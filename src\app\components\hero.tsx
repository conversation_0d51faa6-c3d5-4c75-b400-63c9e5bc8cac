"use client";

import React, { useEffect, useRef } from "react";

const ORANGE = "#e8561c";

const Video = () => {
  const orangeRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!orangeRef.current) return;
      const rect = orangeRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const containerHeight = rect.height;
      const containerTop = rect.top;
      // progress: 0 (Container ganz oben), 1 (Container zu 50% im Viewport)
      const trigger = containerHeight * 0.5;
      const progress = Math.min(Math.max((windowHeight - containerTop) / trigger, 0), 1);
      // Video-Größe: von 75% auf 100%
      const width = 75 + (100 - 75) * progress;
      const height = 75 + (100 - 75) * progress;
      // Border-Radius: bis 90% Progress fast voll, dann langsam auf 0
      let radius;
      if (progress < 0.9) {
        radius = 120 - 20 * progress; // bleibt fast voll
      } else {
        // Nur auf den letzten 10% auf 0
        const eased = (progress - 0.9) / 0.1;
        radius = 100 * (1 - eased);
        radius = Math.max(radius, 0);
      }
      if (videoRef.current) {
        videoRef.current.style.width = `${width}%`;
        videoRef.current.style.height = `${height}%`;
        videoRef.current.style.borderRadius = `${radius}px`;
      }
      orangeRef.current.style.borderRadius = `${radius}px`;
    };
    window.addEventListener("scroll", handleScroll);
    handleScroll();
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <div
      ref={orangeRef}
      style={{
        width: "100%",
        height: "140vh",
        background: ORANGE,
        borderRadius: 120,
        marginTop: "-20vh",
        marginBottom: 0,
        boxShadow: "0 8px 32px rgba(0,0,0,0.08)",
        overflow: "hidden",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        position: "relative",
        zIndex: 2,
        transition: "box-shadow 0.5s cubic-bezier(0.4,0,0.2,1), border-radius 0.4s cubic-bezier(0.4,0,0.2,1)",
      }}
    >
      <video
        ref={videoRef}
        src="/video/mockrocket-export.mp4"
        style={{
          width: "75%",
          height: "75%",
          objectFit: "cover",
          borderRadius: 120,
          background: "#000",
          display: "block",
          transition: "width 0.1s linear, height 0.1s linear, border-radius 0.4s cubic-bezier(0.4,0,0.2,1)",
        }}
        autoPlay
        loop
        muted
        playsInline
      />
    </div>
  );
};

export default function Hero() {
  return (
    <>
      <section className="w-full min-h-screen bg-white relative px-4 sm:px-6 lg:px-8" style={{ fontFamily: 'Satoshi, sans-serif' }}>
        {/* MRVN oben links */}
        <div className="absolute top-6 sm:top-8 left-4 sm:left-6 lg:left-8 z-10">
          <h2 className="text-xl sm:text-2xl font-bold text-black">MRVN</h2>
        </div>

        {/* Zentraler Text */}
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-black leading-tight">
              one person.many ideas.
            </h1>
          </div>
        </div>
      </section>

      {/* Video Container */}
      <Video />
    </>
  );
}

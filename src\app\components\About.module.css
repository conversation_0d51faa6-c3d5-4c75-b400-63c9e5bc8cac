/* Card Styles */
.card {
  position: relative;
  padding: 40px 0;
  width: 100%;
  background: #f0f0f0;
  z-index: 1;
}

.cardLine {
  position: absolute;
  top: 0;
  left: -50vw;
  right: -50vw;
  height: 1px;
  background: #ccc;
  z-index: 1;
}

.year {
  color: #e8561c;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  text-transform: uppercase;
  z-index: 2;
}

.cardText {
  padding-left: 0;
  z-index: 2;
  position: relative;
}

.cardTitle {
  color: #000;
  font-size: 44px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 16px;
}

.cardDesc {
  color: #666;
  font-size: 28px;
  font-weight: 400;
  line-height: 1.4;
  max-width: 600px;
}

/* Tablet Responsive */
@media (max-width: 900px) {
  .card {
    padding: 32px 0;
  }

  .cardLine {
    left: -30px;
    right: -30px;
  }

  .year {
    font-size: 14px;
  }

  .cardText {
    padding-left: 0;
  }
  
  .cardTitle {
    font-size: 36px;
  }
  
  .cardDesc {
    font-size: 22px;
    max-width: 500px;
  }
}

/* Mobile Responsive */
@media (max-width: 600px) {
  .card {
    padding: 24px 0;
  }
  
  .year {
    font-size: 12px;
  }

  .cardText {
    padding-left: 0;
  }
  
  .cardTitle {
    font-size: 28px;
    margin-bottom: 12px;
  }
  
  .cardDesc {
    font-size: 18px;
    max-width: 100%;
  }
}

/* Extra small mobile */
@media (max-width: 390px) {
  .year {
    font-size: 10px;
  }
  
  .cardText {
    padding-left: 0;
  }
  
  .cardTitle {
    font-size: 24px;
  }
  
  .cardDesc {
    font-size: 16px;
  }
}

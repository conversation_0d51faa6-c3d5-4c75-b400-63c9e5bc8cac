"use client";

import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import styles from "./About.module.css";

gsap.registerPlugin(ScrollTrigger);

const ORANGE = "#e8561c";

const careerTimeline = [
  {
    period: "2010–2014",
    title: "Software Developer – Apprenticeship & company experience",
    desc: "Learned and worked in web development, UI/UX, and digital tools",
  },
  {
    period: "2014–2022",
    title: "Technical Specialist – German Armed Forces",
    desc: "Worked on systems, structure, and operations in high-responsibility settings",
  },
  {
    period: "2022–2024",
    title: "IT Leadership & Systems – Academic studies",
    desc: "Focused on strategy, infrastructure, and organizational thinking",
  },
  {
    period: "2025–Present",
    title: "Independent Developer",
    desc: "Building digital experiences at the intersection of code and design",
  },
];

const CARD_HEADER_HEIGHT = 88; // px, Höhe der Überschrift (Jahr + Titel)

const headerHeights = [
  CARD_HEADER_HEIGHT,        // Card 1
  CARD_HEADER_HEIGHT + 50,   // Card 2
  CARD_HEADER_HEIGHT + 50,   // Card 3
  CARD_HEADER_HEIGHT + 40,   // Card 4
];

const getResponsiveStyles = () => {
  const width = typeof window !== 'undefined' ? window.innerWidth : 1200;
  if (width <= 390) {
    // iPhone 12 und kleinere
    return {
      introFontSize: 32,
      introSubFontSize: 20,
      introMargin: '80px 0 40px 0',
      introMarginLeft: -20,
      topLabelTop: 60,
      topLabelLeft: 20,
      topLabelFontSize: 18,
      rightLabelTop: '300px',
      rightLabelRight: 20,
      rightLabelFontSize: 32,
      sectionPadding: '0 20px 0 10px',
      containerMarginTop: 40,
      timelineMarginTop: 60,
      buttonMarginTop: 40,
    };
  } else if (width < 600) {
    // Mobile
    return {
      introFontSize: 40,
      introSubFontSize: 24,
      introMargin: '100px 0 50px 0',
      introMarginLeft: -40,
      topLabelTop: 80,
      topLabelLeft: 20,
      topLabelFontSize: 20,
      rightLabelTop: '400px',
      rightLabelRight: 20,
      rightLabelFontSize: 36,
      sectionPadding: '0 20px 0 10px',
      containerMarginTop: 50,
      timelineMarginTop: 80,
      buttonMarginTop: 50,
    };
  } else if (width < 900) {
    // Tablet
    return {
      introFontSize: 48,
      introSubFontSize: 28,
      introMargin: '140px 0 60px 0',
      introMarginLeft: -80,
      topLabelTop: 100,
      topLabelLeft: 30,
      topLabelFontSize: 24,
      rightLabelTop: '500px',
      rightLabelRight: 30,
      rightLabelFontSize: 42,
      sectionPadding: '0 30px 0 15px',
      containerMarginTop: 60,
      timelineMarginTop: 100,
      buttonMarginTop: 60,
    };
  } else {
    // Desktop
    return {
      introFontSize: 64,
      introSubFontSize: 40,
      introMargin: '180px 0 80px 0',
      introMarginLeft: -150,
      topLabelTop: 120,
      topLabelLeft: 40,
      topLabelFontSize: 28,
      rightLabelTop: '600px',
      rightLabelRight: 40,
      rightLabelFontSize: 54,
      sectionPadding: '0 40px 0 20px',
      containerMarginTop: 80,
      timelineMarginTop: 120,
      buttonMarginTop: 80,
    };
  }
};

export default function About() {
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [responsive, setResponsive] = React.useState(getResponsiveStyles());

  React.useEffect(() => {
    const handleResize = () => setResponsive(getResponsiveStyles());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    cardRefs.current.forEach((card, idx) => {
      if (!card) return;
      const headerHeight = headerHeights[idx] || CARD_HEADER_HEIGHT;
      gsap.fromTo(
        card,
        { y: 0 },
        {
          y: -((idx) * headerHeight),
          scrollTrigger: {
            trigger: card,
            start: "top center+=120",
            end: `+=${headerHeight + 32}`,
            scrub: 2.8,
            pin: false,
            markers: false,
          },
          ease: "power4.out",
        }
      );
    });
  }, []);

  return (
    <section
      style={{
        background: "#f0f0f0",
        width: "100%",
        minHeight: "100vh",
        fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        position: 'relative',
      }}
    >
      {/* Top label - ABOUT at very left of section */}
      <div style={{
        position: 'absolute',
        top: responsive.topLabelTop,
        left: responsive.topLabelLeft,
        color: '#000',
        fontSize: responsive.topLabelFontSize,
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (ABOUT)
      </div>
      {/* Right label - 01 at very right of section */}
      <div style={{
        position: 'absolute',
        top: responsive.rightLabelTop,
        right: responsive.rightLabelRight,
        color: ORANGE,
        fontSize: responsive.rightLabelFontSize,
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (01)
      </div>

      <div style={{
        maxWidth: 1200,
        margin: '0 auto',
        padding: 0,
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        minHeight: '100vh',
        marginTop: responsive.containerMarginTop,
        position: 'relative',
      }}>
        {/* Main content with responsive padding */}
        <div style={{ padding: responsive.sectionPadding }}>
          {/* Intro - responsive sizing */}
          <div style={{
            color: '#000',
            fontSize: responsive.introFontSize,
            fontWeight: 700,
            margin: responsive.introMargin,
            maxWidth: 900,
            alignSelf: 'flex-start',
            lineHeight: 1.15,
            marginLeft: responsive.introMarginLeft,
          }}>
            I'm Marvin, a developer based in Germany.<br />
            <span style={{
              fontWeight: 400,
              fontSize: responsive.introSubFontSize,
              display: 'block',
              marginTop: 32,
              lineHeight: 1.2,
            }}>
              I like solving problems with code, thinking things through properly, and making sure the result feels right – not just technically, but for people too.
            </span>
          </div>

          {/* Timeline Cards mit GSAP Card-Stack Animation und CSS-Modul */}
          <div
            style={{
              width: '100%',
              marginTop: responsive.timelineMarginTop,
              minHeight: '60vh',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: 0,
                justifyContent: 'flex-start',
              }}
            >
              {careerTimeline.map((item, idx) => (
                <div
                  key={item.period}
                  ref={el => { cardRefs.current[idx] = el; }}
                  className={styles.card}
                >
                  {/* Durchgezogene Trennlinie */}
                  <div className={styles.cardLine} />
                  {/* Jahr links absolut */}
                  <div className={styles.year}>
                    ({item.period})
                  </div>
                  {/* Text rechts, mit extra Padding links damit es nicht über das Jahr läuft */}
                  <div className={styles.cardText}>
                    <div className={styles.cardTitle}>
                      {item.title}
                    </div>
                    <div className={styles.cardDesc}>
                      {item.desc}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* More About Me Button */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            marginTop: responsive.buttonMarginTop,
          }}>
            <button
              style={{
                background: 'transparent',
                color: ORANGE,
                border: `1px solid ${ORANGE}`,
                padding: '12px 24px',
                fontSize: 14,
                fontWeight: 600,
                borderRadius: 25,
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
                letterSpacing: 0.5,
                textTransform: 'uppercase',
              }}
              onMouseEnter={(e) => {
                (e.target as HTMLButtonElement).style.background = ORANGE;
                (e.target as HTMLButtonElement).style.color = '#fff';
                (e.target as HTMLButtonElement).style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                (e.target as HTMLButtonElement).style.background = 'transparent';
                (e.target as HTMLButtonElement).style.color = ORANGE;
                (e.target as HTMLButtonElement).style.transform = 'translateY(0)';
              }}
            >
              more about me
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}

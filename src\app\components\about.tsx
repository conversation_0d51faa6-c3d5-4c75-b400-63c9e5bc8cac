"use client";

import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import styles from "./About.module.css";
gsap.registerPlugin(ScrollTrigger);

const ORANGE = "#e8561c";

const careerTimeline = [
  {
    period: "2010–2014",
    title: "Software Developer – Apprenticeship & company experience",
    desc: "Learned and worked in web development, UI/UX, and digital tools",
  },
  {
    period: "2014–2022",
    title: "Technical Specialist – German Armed Forces",
    desc: "Worked on systems, structure, and operations in high-responsibility settings",
  },
  {
    period: "2022–2024",
    title: "IT Leadership & Systems – Academic studies",
    desc: "Focused on strategy, infrastructure, and organizational thinking",
  },
  {
    period: "2025–Present",
    title: "Independent Developer",
    desc: "Building digital experiences at the intersection of code and design",
  },
];

const CARD_HEADER_HEIGHT = 88; // px, Höhe der Überschrift (Jahr + Titel)

const headerHeights = [
  CARD_HEADER_HEIGHT,        // Card 1
  CARD_HEADER_HEIGHT + 48,   // Card 2
  CARD_HEADER_HEIGHT + 50,   // Card 3
  CARD_HEADER_HEIGHT + 43,   // Card 4
];

const getResponsiveStyles = () => {
  const width = typeof window !== 'undefined' ? window.innerWidth : 1200;
  if (width < 600) {
    // Mobile
    return {
      cardPadding: '24px 0 24px 0',
      textPaddingLeft: 80,
      gap: 40,
      yearLeft: -60,
      titleFontSize: 28,
      descFontSize: 18,
    };
  } else if (width < 900) {
    // Tablet
    return {
      cardPadding: '32px 0 32px 0',
      textPaddingLeft: 160,
      gap: 100,
      yearLeft: -90,
      titleFontSize: 36,
      descFontSize: 22,
    };
  } else {
    // Desktop
    return {
      cardPadding: '40px 0 40px 0',
      textPaddingLeft: 320,
      gap: 200,
      yearLeft: -120,
      titleFontSize: 44,
      descFontSize: 28,
    };
  }
};

const About = () => {
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);

  const [responsive, setResponsive] = React.useState(getResponsiveStyles());

  React.useEffect(() => {
    const handleResize = () => setResponsive(getResponsiveStyles());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    cardRefs.current.forEach((card, idx) => {
      if (!card) return;
      const headerHeight = headerHeights[idx] || CARD_HEADER_HEIGHT;
      gsap.fromTo(
        card,
        { y: 0, opacity: 1 },
        {
          y: -((idx) * headerHeight),
          opacity: 1,
          scrollTrigger: {
            trigger: card,
            start: "top center+=120",
            end: `+=${headerHeight + 32}`,
            scrub: 2.8,
            pin: false,
            markers: false,
          },
          ease: "power4.out",
        }
      );
    });
  }, []);

  return (
    <section
      style={{
        background: "#f0f0f0",
        width: "100%",
        minHeight: "100vh",
        fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        position: 'relative',
      }}
    >
      {/* Top label - ABOUT at very left of section */}
      <div style={{
        position: 'absolute',
        top: 120,
        left: 40,
        color: '#000',
        fontSize: 28,
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (ABOUT)
      </div>
      {/* Right label - 01 at very right of section */}
      <div style={{
        position: 'absolute',
        top: '600px',
        right: 40,
        color: ORANGE,
        fontSize: 54,
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (01)
      </div>
      <div style={{
        maxWidth: 1200,
        margin: '0 auto',
        padding: 0,
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        minHeight: '100vh',
        marginTop: 80,
        position: 'relative',
      }}>
        {/* Main content with left padding reduced */}
        <div style={{ padding: '0 40px 0 20px' }}>
          {/* Intro - much larger */}
          <div style={{
            color: '#000',
            fontSize: 64,
            fontWeight: 700,
            margin: '180px 0 80px 0',
            maxWidth: 900,
            alignSelf: 'flex-start',
            lineHeight: 1.15,
            marginLeft: -150,
          }}>
            I'm Marvin, a developer based in Germany.<br />
            <span style={{
              fontWeight: 400,
              fontSize: 40,
              display: 'block',
              marginTop: 32,
              lineHeight: 1.2,
            }}>
              I like solving problems with code, thinking things through properly, and making sure the result feels right – not just technically, but for people too.
            </span>
          </div>
          {/* Timeline Cards mit GSAP Card-Stack Animation und CSS-Modul */}
          <div
            style={{
              width: '100%',
              marginTop: 120,
              minHeight: '60vh',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: 0,
                justifyContent: 'flex-start',
              }}
            >
              {careerTimeline.map((item, idx) => (
                <div
                  key={item.period}
                  ref={el => { cardRefs.current[idx] = el; }}
                  className={styles.card}
                >
                  {/* Durchgezogene Trennlinie */}
                  <div className={styles.cardLine} />

                  {/* Card Content */}
                  <div className={styles.cardText}>
                    {/* Jahr über der Überschrift */}
                    <div className={styles.year}>
                      ({item.period})
                    </div>
                    <div className={styles.cardTitle}>
                      {item.title}
                    </div>
                    <div className={styles.cardDesc}>
                      {item.desc}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* More About Me Button */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            marginTop: 80,
          }}>
            <button
              style={{
                background: 'transparent',
                color: ORANGE,
                border: `1px solid ${ORANGE}`,
                padding: '12px 24px',
                fontSize: 14,
                fontWeight: 600,
                borderRadius: 25,
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
                letterSpacing: 0.5,
                textTransform: 'uppercase',
              }}
              onMouseEnter={(e) => {
                (e.target as HTMLButtonElement).style.background = ORANGE;
                (e.target as HTMLButtonElement).style.color = '#fff';
                (e.target as HTMLButtonElement).style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                (e.target as HTMLButtonElement).style.background = 'transparent';
                (e.target as HTMLButtonElement).style.color = ORANGE;
                (e.target as HTMLButtonElement).style.transform = 'translateY(0)';
              }}
            >
              more about me
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
